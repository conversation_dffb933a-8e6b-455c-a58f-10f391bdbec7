{"name": "facebot-multi", "version": "1.0.0", "description": "Facebook Automation Multi-Profile Application", "main": "src/main.js", "targets": {"main": {"source": "src/main.js", "isLibrary": true}, "renderer": {"source": "src/renderer/index.html", "distDir": "dist/renderer"}}, "scripts": {"start": "npm run dev", "dev": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "test-core": "node test-core.js", "migrate": "node migrate.js", "clean": "rimraf data/ profiles/mock-* dist/", "reset": "npm run clean && npm run test-core"}, "keywords": ["facebook", "automation", "multi-profile", "electron", "playwright"], "author": "Mujib", "license": "MIT", "dependencies": {"uuid": "^9.0.1", "electron-store": "^8.1.0", "ws": "^8.14.2", "node-cron": "^3.0.3"}, "devDependencies": {"electron": "^28.0.0", "parcel": "^2.10.0", "concurrently": "^8.2.2", "@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@parcel/transformer-babel": "^2.14.4", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "rimraf": "^5.0.5"}, "build": {"appId": "com.mujib.facebot-multi", "productName": "FaceBot Multi", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}