const fs = require('fs');
const path = require('path');
const BrowserConnector = require('./BrowserConnector');

class FacebookAutomation {
    constructor(profileManager, dbManager, smartSelectorManager) {
        this.profileManager = profileManager;
        this.dbManager = dbManager;
        this.smartSelectorManager = smartSelectorManager;
        this.browserConnector = new BrowserConnector(profileManager, dbManager);
        this.activeAutomations = new Map(); // Map<automationId, { profileId, type, status }>
    }

    /**
     * Share content to Facebook groups with smart selectors
     * @param {object} options - Automation options
     * @returns {Promise<object>} Automation result
     */
    async shareToGroups(options) {
        const {
            profileId,
            content,
            groups,
            decoyLinks = [],
            sequenceDelay = { min: 30000, max: 60000 },
            randomScrolling = true,
            antiDetection = true
        } = options;

        try {
            console.log(`🚀 Starting Facebook group sharing for profile: ${profileId}`);

            const automationId = this.generateAutomationId();
            this.activeAutomations.set(automationId, {
                profileId,
                type: 'group_sharing',
                status: 'running',
                startTime: Date.now(),
                totalGroups: groups.length,
                completedGroups: 0,
                errors: []
            });

            // Launch browser for automation
            const browserResult = await this.browserConnector.launchBrowserForAutomation(profileId, {
                initialUrl: 'https://facebook.com'
            });
            if (!browserResult.success) {
                throw new Error(`Failed to launch browser: ${browserResult.error}`);
            }

            const results = {
                automationId,
                profileId,
                totalGroups: groups.length,
                successfulShares: 0,
                failedShares: 0,
                errors: [],
                decoyActions: 0
            };

            // Process each group with sequence posting
            for (let i = 0; i < groups.length; i++) {
                const group = groups[i];

                try {
                    console.log(`📝 Sharing to group ${i + 1}/${groups.length}: ${group.name || group.url}`);

                    // Anti-detection: Random scrolling before action
                    if (randomScrolling && Math.random() < 0.7) {
                        await this.performRandomScrolling();
                        results.decoyActions++;
                    }

                    // Navigate to group
                    const navigationResult = await this.navigateToGroup(profileId, group.url);
                    if (!navigationResult.success) {
                        throw new Error(`Navigation failed: ${navigationResult.error}`);
                    }

                    // Share content using smart selectors
                    const shareResult = await this.shareContentToGroup(profileId, content, group);
                    if (shareResult.success) {
                        results.successfulShares++;
                        console.log(`✅ Successfully shared to: ${group.name || group.url}`);
                    } else {
                        throw new Error(shareResult.error);
                    }

                    // Update automation status
                    const automation = this.activeAutomations.get(automationId);
                    automation.completedGroups = i + 1;
                    automation.lastAction = `Shared to ${group.name || group.url}`;

                    // Anti-detection: Decoy link visits
                    if (decoyLinks.length > 0 && Math.random() < 0.3) {
                        const decoyLink = decoyLinks[Math.floor(Math.random() * decoyLinks.length)];
                        await this.visitDecoyLink(decoyLink);
                        results.decoyActions++;
                    }

                    // Sequence delay between posts
                    if (i < groups.length - 1) {
                        const delay = this.randomDelay(sequenceDelay.min, sequenceDelay.max);
                        console.log(`⏱️ Waiting ${Math.round(delay/1000)}s before next group...`);
                        await this.sleep(delay);
                    }

                } catch (error) {
                    console.error(`❌ Failed to share to group: ${group.name || group.url}`, error.message);
                    results.failedShares++;
                    results.errors.push({
                        group: group.name || group.url,
                        error: error.message,
                        timestamp: Date.now()
                    });

                    // Update automation errors
                    const automation = this.activeAutomations.get(automationId);
                    automation.errors.push({
                        group: group.name || group.url,
                        error: error.message
                    });
                }
            }

            // Complete automation
            const automation = this.activeAutomations.get(automationId);
            automation.status = 'completed';
            automation.endTime = Date.now();
            automation.duration = automation.endTime - automation.startTime;

            // Log automation result
            await this.dbManager.saveLog({
                type: 'automation',
                profileId,
                action: 'group_sharing',
                status: results.failedShares === 0 ? 'success' : 'partial_success',
                details: results,
                timestamp: Date.now()
            });

            console.log(`✅ Group sharing completed: ${results.successfulShares}/${results.totalGroups} successful`);
            return { success: true, results };

        } catch (error) {
            console.error('❌ Facebook group sharing failed:', error.message);

            // Update automation status
            if (this.activeAutomations.has(automationId)) {
                const automation = this.activeAutomations.get(automationId);
                automation.status = 'failed';
                automation.error = error.message;
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Navigate to Facebook group using real browser
     * @param {string} profileId - Profile ID
     * @param {string} groupUrl - Group URL
     * @returns {Promise<object>} Navigation result
     */
    async navigateToGroup(profileId, groupUrl) {
        try {
            console.log(`🔗 Navigating to group: ${groupUrl}`);

            // Navigate using browser connector
            const result = await this.browserConnector.navigateToUrl(profileId, groupUrl);
            if (!result.success) {
                throw new Error(result.error);
            }

            // Wait for page to load
            await this.sleep(this.randomDelay(3000, 6000));

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Share content to group using real browser and smart selectors
     * @param {string} profileId - Profile ID
     * @param {string} content - Content to share
     * @param {object} group - Group information
     * @returns {Promise<object>} Share result
     */
    async shareContentToGroup(profileId, content, group) {
        try {
            console.log(`📝 Sharing content to group: ${group.name || group.url}`);

            // Get smart selectors for Facebook group posting
            const selectors = await this.smartSelectorManager.getSelectors('facebook_group_post');

            console.log(`🎯 Using smart selectors for posting...`);

            // Step 1: Find and click post box
            const postBoxSelectors = [
                '[data-testid="status-attachment-mentions-input"]',
                '[role="textbox"][data-testid="post-composer-input"]',
                'div[role="textbox"][contenteditable="true"]',
                'textarea[placeholder*="What\'s on your mind"]',
                'div[data-testid="post-composer-text-input"]'
            ];

            let postBoxFound = false;
            for (const selector of postBoxSelectors) {
                const clickResult = await this.browserConnector.clickElement(profileId, selector);
                if (clickResult.success && clickResult.result?.found) {
                    console.log(`✅ Found post box with selector: ${selector}`);
                    postBoxFound = true;

                    // Learn this successful selector
                    await this.smartSelectorManager.learnSelector('facebook_group_post_box', selector);
                    break;
                }
                await this.sleep(1000); // Wait between attempts
            }

            if (!postBoxFound) {
                throw new Error('Could not find post box');
            }

            // Step 2: Wait for post box to be ready
            await this.sleep(this.randomDelay(2000, 4000));

            // Step 3: Type content
            const activeElement = await this.browserConnector.executeScript(profileId, `
                document.activeElement.innerText = '${content.replace(/'/g, "\\'")}';
                document.activeElement.dispatchEvent(new Event('input', { bubbles: true }));
                return { success: true };
            `);

            if (!activeElement.success) {
                throw new Error('Failed to type content');
            }

            console.log(`✅ Content typed successfully`);

            // Step 4: Wait before posting
            await this.sleep(this.randomDelay(2000, 4000));

            // Step 5: Find and click post button
            const postButtonSelectors = [
                '[data-testid="react-composer-post-button"]',
                '[aria-label="Post"]',
                'div[role="button"][tabindex="0"]:has-text("Post")',
                'button:has-text("Post")',
                '[data-testid="post-button"]'
            ];

            let postButtonFound = false;
            for (const selector of postButtonSelectors) {
                const clickResult = await this.browserConnector.clickElement(profileId, selector);
                if (clickResult.success && clickResult.result?.found) {
                    console.log(`✅ Found post button with selector: ${selector}`);
                    postButtonFound = true;

                    // Learn this successful selector
                    await this.smartSelectorManager.learnSelector('facebook_group_post_button', selector);
                    break;
                }
                await this.sleep(1000);
            }

            if (!postButtonFound) {
                throw new Error('Could not find post button');
            }

            // Step 6: Wait for post to be submitted
            await this.sleep(this.randomDelay(3000, 6000));

            console.log(`✅ Content posted successfully to group`);

            // Update smart selector success
            await this.smartSelectorManager.learnSelector('facebook_group_post', 'complete_flow');

            return { success: true };
        } catch (error) {
            console.error(`❌ Failed to share content:`, error.message);
            // Record selector failure for learning
            await this.smartSelectorManager.recordFailure('facebook_group_post', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Perform random scrolling for anti-detection
     * @returns {Promise<void>}
     */
    async performRandomScrolling() {
        try {
            console.log(`🔄 Performing random scrolling for anti-detection...`);

            // Simulate random scrolling
            const scrollActions = Math.floor(Math.random() * 3) + 1;
            for (let i = 0; i < scrollActions; i++) {
                await this.sleep(this.randomDelay(1000, 3000));
            }

        } catch (error) {
            console.warn('Random scrolling failed:', error.message);
        }
    }

    /**
     * Visit decoy link for anti-detection
     * @param {string} decoyUrl - Decoy URL to visit
     * @returns {Promise<void>}
     */
    async visitDecoyLink(decoyUrl) {
        try {
            console.log(`🎭 Visiting decoy link: ${decoyUrl}`);

            // Simulate decoy link visit
            await this.sleep(this.randomDelay(5000, 15000));

        } catch (error) {
            console.warn('Decoy link visit failed:', error.message);
        }
    }

    /**
     * Get automation status
     * @param {string} automationId - Automation ID
     * @returns {object|null} Automation status
     */
    getAutomationStatus(automationId) {
        return this.activeAutomations.get(automationId) || null;
    }

    /**
     * Stop automation
     * @param {string} automationId - Automation ID
     * @returns {boolean} Success status
     */
    stopAutomation(automationId) {
        const automation = this.activeAutomations.get(automationId);
        if (automation) {
            automation.status = 'stopped';
            automation.endTime = Date.now();
            console.log(`⏹️ Automation stopped: ${automationId}`);
            return true;
        }
        return false;
    }

    /**
     * Get all active automations
     * @returns {Array} Array of active automations
     */
    getActiveAutomations() {
        return Array.from(this.activeAutomations.entries()).map(([id, data]) => ({
            id,
            ...data
        }));
    }

    /**
     * Generate unique automation ID
     * @returns {string} Automation ID
     */
    generateAutomationId() {
        return `automation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Generate random delay
     * @param {number} min - Minimum delay in ms
     * @param {number} max - Maximum delay in ms
     * @returns {number} Random delay
     */
    randomDelay(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * Sleep for specified duration
     * @param {number} ms - Duration in milliseconds
     * @returns {Promise<void>}
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Cleanup all automations
     * @returns {Promise<void>}
     */
    async cleanup() {
        console.log('🧹 Cleaning up Facebook automations...');

        // Stop all active automations
        for (const [automationId] of this.activeAutomations) {
            this.stopAutomation(automationId);
        }

        this.activeAutomations.clear();
        console.log('✅ Facebook automation cleanup complete');
    }
}

module.exports = FacebookAutomation;
